<?php $__env->startSection('page-title', 'Dashboard'); ?>

<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        Dashboard
     <?php $__env->endSlot(); ?>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Total Users -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                    <i class="fas fa-users text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Utenti Totali</p>
                    <p class="text-2xl font-bold text-gray-900"><?php echo e($stats['total_users']); ?></p>
                </div>
            </div>
        </div>

        <!-- Total Clients -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100 text-green-600">
                    <i class="fas fa-address-book text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Clienti Totali</p>
                    <p class="text-2xl font-bold text-gray-900"><?php echo e($stats['total_clients']); ?></p>
                </div>
            </div>
        </div>

        <!-- Active Projects -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                    <i class="fas fa-project-diagram text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Progetti Attivi</p>
                    <p class="text-2xl font-bold text-gray-900"><?php echo e($stats['active_projects']); ?></p>
                </div>
            </div>
        </div>

        <!-- Pending Payments -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-red-100 text-red-600">
                    <i class="fas fa-credit-card text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Pagamenti in Attesa</p>
                    <p class="text-2xl font-bold text-gray-900"><?php echo e($stats['pending_payments']); ?></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Revenue and Expenses -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Monthly Revenue -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Entrate Questo Mese</h3>
            <div class="text-3xl font-bold text-green-600">€<?php echo e(number_format($stats['total_revenue_this_month'], 2)); ?></div>
            <p class="text-sm text-gray-600 mt-2">Pagamenti completati</p>
        </div>

        <!-- Monthly Expenses -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Spese Questo Mese</h3>
            <div class="text-3xl font-bold text-red-600">€<?php echo e(number_format($stats['total_expenses_this_month'], 2)); ?></div>
            <p class="text-sm text-gray-600 mt-2">Spese sostenute</p>
        </div>
    </div>

    <!-- Recent Projects and Payments -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Recent Projects -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Progetti Recenti</h3>
            </div>
            <div class="p-6">
                <?php if($recent_projects->count() > 0): ?>
                    <div class="space-y-4">
                        <?php $__currentLoopData = $recent_projects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $project): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                <div>
                                    <h4 class="font-medium text-gray-900"><?php echo e($project->name); ?></h4>
                                    <p class="text-sm text-gray-600"><?php echo e($project->client->full_name); ?></p>
                                    <p class="text-xs text-gray-500"><?php echo e($project->created_at->format('d/m/Y')); ?></p>
                                </div>
                                <span class="px-2 py-1 text-xs font-medium rounded-full
                                    <?php if($project->status === 'completed'): ?> bg-green-100 text-green-800
                                    <?php elseif($project->status === 'in_progress'): ?> bg-blue-100 text-blue-800
                                    <?php elseif($project->status === 'planning'): ?> bg-yellow-100 text-yellow-800
                                    <?php else: ?> bg-gray-100 text-gray-800 <?php endif; ?>">
                                    <?php echo e(ucfirst($project->status)); ?>

                                </span>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php else: ?>
                    <p class="text-gray-500 text-center py-8">Nessun progetto recente</p>
                <?php endif; ?>
            </div>
        </div>

        <!-- Upcoming Payments -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Pagamenti in Scadenza</h3>
            </div>
            <div class="p-6">
                <?php if($upcoming_payments->count() > 0): ?>
                    <div class="space-y-4">
                        <?php $__currentLoopData = $upcoming_payments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $payment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                <div>
                                    <h4 class="font-medium text-gray-900"><?php echo e($payment->project->name); ?></h4>
                                    <p class="text-sm text-gray-600"><?php echo e($payment->client->full_name); ?></p>
                                    <p class="text-xs text-gray-500">Scadenza: <?php echo e($payment->due_date->format('d/m/Y')); ?></p>
                                </div>
                                <div class="text-right">
                                    <p class="font-medium text-gray-900">€<?php echo e(number_format($payment->amount, 2)); ?></p>
                                    <?php if($payment->due_date->isPast()): ?>
                                        <span class="text-xs text-red-600">Scaduto</span>
                                    <?php else: ?>
                                        <span class="text-xs text-green-600"><?php echo e($payment->due_date->diffForHumans()); ?></span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php else: ?>
                    <p class="text-gray-500 text-center py-8">Nessun pagamento in scadenza</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Desktop\Programmarti Gestionale\programmarti-gest\resources\views/dashboard.blade.php ENDPATH**/ ?>