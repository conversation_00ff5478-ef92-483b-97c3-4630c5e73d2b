# MySQL Export/Import Commands for Works Management Feature

## Export Commands (for backup before installation)

# Export complete database structure and data
mysqldump -u root -p programmarti_gestionale > backup_before_works_$(date +%Y%m%d_%H%M%S).sql

# Export only structure (no data)
mysqldump -u root -p --no-data programmarti_gestionale > structure_backup_$(date +%Y%m%d_%H%M%S).sql

# Export specific tables (if you want to backup only certain tables)
mysqldump -u root -p programmarti_gestionale users projects clients > essential_tables_backup_$(date +%Y%m%d_%H%M%S).sql

## Import Commands (for installation)

# Import the works management update
mysql -u root -p programmarti_gestionale < database_works_update.sql

# Import complete database (if restoring from backup)
mysql -u root -p programmarti_gestionale < backup_before_works_YYYYMMDD_HHMMSS.sql

## Verification Commands

# Connect to MySQL and verify installation
mysql -u root -p programmarti_gestionale

# Once connected, run these queries:
SHOW TABLES LIKE 'works';
SELECT * FROM permissions WHERE name = 'manage works';
SELECT COUNT(*) as works_count FROM works;
DESCRIBE works;

## Quick Installation Script (Linux/Mac)

#!/bin/bash
# Save this as install_works.sh and run with: bash install_works.sh

echo "Starting Works Management installation..."

# Backup current database
echo "Creating backup..."
mysqldump -u root -p programmarti_gestionale > backup_before_works_$(date +%Y%m%d_%H%M%S).sql

# Import works management update
echo "Installing works management..."
mysql -u root -p programmarti_gestionale < database_works_update.sql

# Verify installation
echo "Verifying installation..."
mysql -u root -p programmarti_gestionale -e "SELECT 'Works table created' as status, COUNT(*) as exists FROM information_schema.tables WHERE table_schema = 'programmarti_gestionale' AND table_name = 'works';"

echo "Installation complete!"

## Windows Batch Script

REM Save this as install_works.bat and run it

@echo off
echo Starting Works Management installation...

REM Backup current database
echo Creating backup...
mysqldump -u root -p programmarti_gestionale > backup_before_works_%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%.sql

REM Import works management update
echo Installing works management...
mysql -u root -p programmarti_gestionale < database_works_update.sql

echo Installation complete!
pause

## Alternative Laravel Artisan Commands (Recommended)

# If you prefer using Laravel's built-in tools:

# Run migration
php artisan migrate

# Seed permissions and sample data
php artisan db:seed --class=RoleSeeder
php artisan db:seed --class=WorkSeeder

# Clear cache
php artisan cache:clear
php artisan config:clear

## Database Connection Settings

# Make sure your .env file has correct database settings:
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=programmarti_gestionale
DB_USERNAME=root
DB_PASSWORD=your_password

## Troubleshooting

# If you get permission errors:
GRANT ALL PRIVILEGES ON programmarti_gestionale.* TO 'root'@'localhost';
FLUSH PRIVILEGES;

# If foreign key constraints fail:
SET FOREIGN_KEY_CHECKS=0;
# Run your import command
SET FOREIGN_KEY_CHECKS=1;

# Check MySQL version compatibility:
SELECT VERSION();

## Post-Installation Verification

# Check if all components are working:
mysql -u root -p programmarti_gestionale -e "
SELECT 'Database Check' as test_name, 'PASS' as result;
SELECT 'Works Table' as test_name, IF(COUNT(*) > 0, 'PASS', 'FAIL') as result FROM information_schema.tables WHERE table_schema = 'programmarti_gestionale' AND table_name = 'works';
SELECT 'Permission Added' as test_name, IF(COUNT(*) > 0, 'PASS', 'FAIL') as result FROM permissions WHERE name = 'manage works';
SELECT 'Sample Data' as test_name, CONCAT(COUNT(*), ' records') as result FROM works;
"
