@section('page-title', 'Gestione Appuntamenti')

<x-app-layout>
    <x-slot name="header">
        Gestione Appuntamenti
    </x-slot>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <!-- Header with Filters and Add Button -->
        <div class="p-6 border-b border-gray-200">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-xl font-semibold text-gray-900">Lista Appuntamenti</h2>
                <a href="{{ route('appointments.create') }}" 
                   class="text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 hover:opacity-90"
                   style="background-color: #007BCE;"
                   onmouseover="this.style.backgroundColor='#005B99'"
                   onmouseout="this.style.backgroundColor='#007BCE'">
                    <i class="fas fa-plus mr-2"></i>
                    Nuovo Appuntamento
                </a>
            </div>

            <!-- Filters -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label for="date-filter" class="block text-sm font-medium text-gray-700 mb-1">
                        Filtra per Data
                    </label>
                    <input type="date" 
                           id="date-filter" 
                           value="{{ $filterDate }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div>
                    <label for="client-filter" class="block text-sm font-medium text-gray-700 mb-1">
                        Filtra per Cliente
                    </label>
                    <select id="client-filter" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">Tutti i clienti</option>
                        @foreach($clients as $client)
                            <option value="{{ $client->id }}" {{ $filterClient == $client->id ? 'selected' : '' }}>
                                {{ $client->full_name }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <div class="flex items-end space-x-2">
                    <button type="button"
                            id="clear-filters"
                            class="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                        <i class="fas fa-times mr-2"></i>
                        Pulisci Filtri
                    </button>
                    <button type="button"
                            id="test-ajax"
                            class="px-4 py-2 text-blue-600 border border-blue-300 rounded-lg hover:bg-blue-50 transition-colors duration-200">
                        <i class="fas fa-flask mr-2"></i>
                        Test AJAX
                    </button>
                </div>
            </div>
        </div>

        <!-- Appointments Table -->
        <div id="appointments-table">
            @include('appointments.partials.table', ['appointments' => $appointments])
        </div>
    </div>

    @push('scripts')
    <script>
        // Wrap everything in an IIFE to avoid conflicts
        (function() {
            'use strict';

            console.log('🚀 Appointments JavaScript loading...'); // Debug log

            // Wait for both DOM and Alpine to be ready
            function initializeAppointments() {
                console.log('🔍 Initializing appointments...'); // Debug log

                const dateFilter = document.getElementById('date-filter');
                const clientFilter = document.getElementById('client-filter');
                const clearFilters = document.getElementById('clear-filters');
                const testAjax = document.getElementById('test-ajax');
                const appointmentsTable = document.getElementById('appointments-table');

                // Check if all elements exist
                if (!dateFilter || !clientFilter || !clearFilters || !testAjax || !appointmentsTable) {
                    console.error('❌ Required DOM elements not found:', {
                        dateFilter: !!dateFilter,
                        clientFilter: !!clientFilter,
                        clearFilters: !!clearFilters,
                        testAjax: !!testAjax,
                        appointmentsTable: !!appointmentsTable
                    });
                    return;
                }

                console.log('✅ All DOM elements found'); // Debug log

                // Function to show loading state
                function showLoading() {
                    console.log('⏳ Showing loading state'); // Debug log
                    appointmentsTable.style.opacity = '0.5';
                    appointmentsTable.style.pointerEvents = 'none';

                    // Add loading indicator
                    const loadingDiv = document.createElement('div');
                    loadingDiv.id = 'loading-indicator';
                    loadingDiv.className = 'absolute inset-0 flex items-center justify-center bg-white bg-opacity-75 z-10';
                    loadingDiv.innerHTML = '<div class="flex items-center space-x-2"><div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div><span class="text-gray-600">Caricamento...</span></div>';
                    appointmentsTable.style.position = 'relative';
                    appointmentsTable.appendChild(loadingDiv);
                }

                // Function to hide loading state
                function hideLoading() {
                    console.log('✅ Hiding loading state'); // Debug log
                    appointmentsTable.style.opacity = '1';
                    appointmentsTable.style.pointerEvents = 'auto';

                    // Remove loading indicator
                    const loadingDiv = document.getElementById('loading-indicator');
                    if (loadingDiv) {
                        loadingDiv.remove();
                    }
                }

                function updateTable() {
                    const date = dateFilter.value;
                    const clientId = clientFilter.value;

                    console.log('🔄 Updating table with filters:', { date, clientId }); // Debug log

                    showLoading();

                    // Build the URL with parameters
                    const params = new URLSearchParams();
                    if (date) {
                        params.append('date', date);
                    }
                    if (clientId) {
                        params.append('client_id', clientId);
                    }

                    const url = `{{ route('appointments.index') }}${params.toString() ? '?' + params.toString() : ''}`;
                    console.log('🌐 Fetching URL:', url); // Debug log

                    fetch(url, {
                        method: 'GET',
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                            'Accept': 'text/html'
                        }
                    })
                    .then(response => {
                        console.log('📡 Response status:', response.status); // Debug log
                        if (!response.ok) {
                            return response.text().then(text => {
                                console.error('❌ Error response body:', text);
                                throw new Error(`HTTP error! status: ${response.status}`);
                            });
                        }
                        return response.text();
                    })
                    .then(html => {
                        console.log('📄 Received HTML length:', html.length); // Debug log
                        console.log('📄 HTML preview:', html.substring(0, 200)); // Debug log
                        appointmentsTable.innerHTML = html;
                        attachStatusHandlers();
                        hideLoading();

                        // Show success feedback
                        showToast('Filtri applicati', 'La tabella è stata aggiornata con successo', 'success');
                    })
                    .catch(error => {
                        console.error('❌ Error updating table:', error);
                        hideLoading();
                        showToast('Errore', 'Errore durante l\'aggiornamento della tabella: ' + error.message, 'error');
                    });
                }

                function attachStatusHandlers() {
                    console.log('🔗 Attaching status handlers...'); // Debug log
                    const statusSelects = document.querySelectorAll('.status-select');
                    console.log('📋 Found status selects:', statusSelects.length); // Debug log

                    statusSelects.forEach((select, index) => {
                        console.log(`🎯 Attaching handler to select ${index + 1}:`, select.dataset.appointmentId); // Debug log

                        // Remove any existing listeners to prevent duplicates
                        const newSelect = select.cloneNode(true);
                        select.parentNode.replaceChild(newSelect, select);

                        newSelect.addEventListener('change', function() {
                            const appointmentId = this.dataset.appointmentId;
                            const status = this.value;
                            const originalValue = this.dataset.originalValue || this.value;

                            console.log('🔄 Updating status for appointment:', appointmentId, 'to:', status); // Debug log

                            // Disable the select while updating
                            this.disabled = true;

                            fetch(`/appointments/${appointmentId}/status`, {
                                method: 'PATCH',
                                headers: {
                                    'Content-Type': 'application/json',
                                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                                    'X-Requested-With': 'XMLHttpRequest',
                                    'Accept': 'application/json'
                                },
                                body: JSON.stringify({ status: status })
                            })
                            .then(response => {
                                console.log('📡 Status update response:', response.status); // Debug log
                                if (!response.ok) {
                                    return response.text().then(text => {
                                        console.error('❌ Status update error response:', text);
                                        throw new Error(`HTTP error! status: ${response.status}`);
                                    });
                                }
                                return response.json();
                            })
                            .then(data => {
                                console.log('✅ Status update data:', data); // Debug log
                                if (data.success) {
                                    const statusBadge = document.querySelector(`#status-badge-${appointmentId}`);
                                    if (statusBadge) {
                                        statusBadge.textContent = data.status_label;
                                        statusBadge.className = `px-2 py-1 text-xs font-medium rounded-full ${data.status_color}`;
                                    }

                                    // Update the original value for future reference
                                    this.dataset.originalValue = status;

                                    showToast('Status aggiornato', data.message, 'success');
                                } else {
                                    throw new Error(data.message || 'Errore sconosciuto');
                                }
                            })
                            .catch(error => {
                                console.error('❌ Error updating status:', error);
                                // Revert the select to original value
                                this.value = originalValue;
                                showToast('Errore', 'Errore durante l\'aggiornamento dello status', 'error');
                            })
                            .finally(() => {
                                // Re-enable the select
                                this.disabled = false;
                            });
                        });

                        // Store original value for error recovery
                        newSelect.dataset.originalValue = newSelect.value;
                    });
                }

                // Toast notification function (simplified version)
                function showToast(title, message, type = 'info') {
                    console.log(`🍞 Showing toast: ${title} - ${message} (${type})`); // Debug log
                    const toast = document.createElement('div');
                    toast.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm transform transition-all duration-300`;

                    const bgColor = type === 'success' ? 'bg-green-500' :
                                   type === 'error' ? 'bg-red-500' : 'bg-blue-500';

                    toast.classList.add(bgColor);

                    toast.innerHTML = `
                        <div class="flex items-start text-white">
                            <div class="flex-shrink-0">
                                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                            </div>
                            <div class="ml-3 flex-1">
                                <p class="text-sm font-medium">${title}</p>
                                <p class="text-sm opacity-90">${message}</p>
                            </div>
                            <button onclick="this.parentElement.parentElement.remove()" class="ml-4 hover:text-gray-200">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    `;

                    document.body.appendChild(toast);

                    // Auto remove after 3 seconds
                    setTimeout(() => {
                        if (toast.parentElement) {
                            toast.remove();
                        }
                    }, 3000);
                }

                // Event listeners for filters
                console.log('🎧 Adding event listeners...'); // Debug log
                dateFilter.addEventListener('change', function() {
                    console.log('📅 Date filter changed to:', this.value); // Debug log
                    updateTable();
                });

                clientFilter.addEventListener('change', function() {
                    console.log('👤 Client filter changed to:', this.value); // Debug log
                    updateTable();
                });

                clearFilters.addEventListener('click', function() {
                    console.log('🧹 Clear filters clicked'); // Debug log
                    dateFilter.value = '{{ \Carbon\Carbon::today()->format('Y-m-d') }}';
                    clientFilter.value = '';
                    updateTable();
                });

                testAjax.addEventListener('click', function() {
                    console.log('🧪 Test AJAX clicked'); // Debug log

                    fetch('/appointments-test-ajax', {
                        method: 'GET',
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                            'Accept': 'application/json'
                        }
                    })
                    .then(response => {
                        console.log('🧪 Test AJAX response status:', response.status);
                        return response.json();
                    })
                    .then(data => {
                        console.log('🧪 Test AJAX data:', data);
                        showToast('Test AJAX', 'AJAX connectivity test successful!', 'success');
                    })
                    .catch(error => {
                        console.error('🧪 Test AJAX error:', error);
                        showToast('Test AJAX Failed', 'AJAX connectivity test failed: ' + error.message, 'error');
                    });
                });

                // Initial attachment of status handlers
                console.log('🎯 Initial attachment of status handlers'); // Debug log
                attachStatusHandlers();

                console.log('✅ Appointments initialization complete!'); // Debug log
            }

            // Initialize when DOM is ready
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', initializeAppointments);
            } else {
                // DOM is already ready
                initializeAppointments();
            }

            // Also initialize after a short delay to handle any Alpine.js conflicts
            setTimeout(initializeAppointments, 100);

        })(); // End IIFE
    </script>
    @endpush
</x-app-layout>
