---
deployment:
  tasks:
    - export DEPLOYPATH=/public_html/
    - /bin/cp -R * $DEPLOYPATH
    - cd $DEPLOYPATH
    - composer install --no-dev --optimize-autoloader
    - npm install --production
    - npm run build
    - php artisan config:cache
    - php artisan route:cache
    - php artisan view:cache
    - php artisan migrate --force
    - chmod -R 755 storage bootstrap/cache
    - chown -R $USER:$USER storage bootstrap/cache